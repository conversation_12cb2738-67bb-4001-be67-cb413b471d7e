define spleepv1 = False
define swtv = False
define deal = False


image czr:
    "1a/rs1.jpg"
    3.0
    "1a/rs3.jpg"
    0.2
    repeat

image fig:
    "1a/a01.jpg"
    0.3
    "1a/a02.jpg"
    0.3
    "1a/a03.jpg"
    0.3
    "1a/a04.jpg"
    0.3
    "1a/a05.jpg"
    0.3
    "1a/a06.jpg"
    0.3
    "1a/a07.jpg"
    0.3
    "1a/a08.jpg"
    0.3
    "1a/a09.jpg"
    0.3
    "1a/a010.jpg"
    0.3
    "1a/a011.jpg"
    0.3
    repeat

image lig:
    "1a/b01.jpg"
    0.3
    "1a/b02.jpg"
    0.3
    "1a/b03.jpg"
    0.3
    repeat

image lig1:
    "1a/c03.jpg"
    0.4
    "1a/c04.jpg"
    0.5
    "1a/c05.jpg"
    0.4
    repeat

image lig2:
    "1a/c012.jpg"
    0.5
    "1a/c013.jpg"
    0.5
    "1a/c014.jpg"
    0.5
    repeat


image dvrp:
    "1a/d01.jpg"
    0.7
    "1a/d02.jpg"
    0.7
    "1a/d03.jpg"
    0.7
    "1a/d04.jpg"
    0.7
    "1a/d05.jpg"
    0.7
    "1a/d06.jpg"
    0.7



label fhyr6:
    if code:
        jump er45gv
    scene q135
    en "Wouldn't you like to have the most secure hotel?"
    scene q144
    a "Yes, but why in the guest rooms?"
    scene q136
    en "Wouldn't you like to know what your guests do when they are alone?"
    scene q145
    a "No, not really."
    scene q137
    en "Why not? That way, you can take care of your guests and make sure nothing happens to them."
    scene q146
    a "Hmmm, is that why you want to install them?"
    scene q138
    en "Yes, we can also watch the cameras together if you want."
    scene q147
    a "But I don't want to spend all my time looking after them. I also want to have fun."
    scene q139
    en "You can do this during times when you have nothing to do, and in that way, you'll see that everything is in order, that the girls are fine and that the maids are working."
    scene q148
    a "(It makes sense, but I feel that she is doing it for other reasons, am I okay with her installing cameras everywhere?)"
    jump chocms




screen textt():
    vbox:
        xalign 0.5 ypos 0.5
        text ("10 minutes later.")
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc
