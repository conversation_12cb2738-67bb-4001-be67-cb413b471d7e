Sun Sep 21 14:00:53 2025
Windows-10-10.0.26100
Ren'Py 7.4.11.2266
 

Bootstrap to the start of init.init took 0.05s
Early init took 0.00s
Loader init took 0.04s
Loading error handling took 0.02s
Loading script took 0.18s
Loading save slot metadata. took 0.01s
Loading persistent took 0.02s
Importing _renpysteam: ImportError('No module named _renpysteam',)
Set script version to: (7, 4, 10)
Running init code took 0.05s
Loading analysis data took 0.01s
Analyze and compile ATL took 0.00s
Index archives took 0.00s
Dump and make backups. took 0.00s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc. took 0.02s
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: "Couldn't load nvlib." (can be ignored)
Creating interface object took 0.00s
Cleaning stores took 0.00s
Init translation took 0.03s
Build styles took 0.00s
Load screen analysis took 0.01s
Analyze screens took 0.00s
Save screen analysis took 0.00s
Prepare screens took 0.03s
Save pyanalysis. took 0.00s
Save bytecode. took 0.00s
Running _start took 0.00s
Performance test:
Interface start took 0.09s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: 'Intel'
Renderer: 'Intel(R) Iris(R) Xe Graphics'
Version: '4.6.0 - Build 32.0.101.6913'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1662, 935) drawable=(1662, 935)
Maximum texture size: 4096x4096
Cleaning stores took 0.00s
Init translation took 0.00s
Build styles took 0.00s
Load screen analysis took 0.01s
Analyze screens took 0.00s
Prepare screens took 0.00s
Running _start took 0.00s
