# This file has been automatically generated by module/uguu/generate_required_functions.py,
# please do not edit it by hand.

from __future__ import division, absolute_import, with_statement, print_function, unicode_literals
from renpy.compat import *

required_functions = [
    "glActiveTexture",
    "glAttachShader",
    "glBindFramebuffer",
    "glBindTexture",
    "glBlendFunc",
    "glClear",
    "glClearColor",
    "glCompileShader",
    "glCopyTexImage2D",
    "glCopyTexSubImage2D",
    "glCreateProgram",
    "glCreateShader",
    "glDeleteFramebuffers",
    "glDeleteProgram",
    "glDeleteShader",
    "glDeleteTextures",
    "glDisable",
    "glDisableVertexAttribArray",
    "glDrawArrays",
    "glEnable",
    "glEnableVertexAttribArray",
    "glFinish",
    "glFramebufferTexture2D",
    "glGenFramebuffers",
    "glGenTextures",
    "glGetAttribLocation",
    "glGetError",
    "glGetIntegerv",
    "glGetProgramInfoLog",
    "glGetProgramiv",
    "glGetShaderInfoLog",
    "glGetShaderiv",
    "glGetString",
    "glGetUniformLocation",
    "glLinkProgram",
    "glReadPixels",
    "glScissor",
    "glShaderSource",
    "glTexImage2D",
    "glTexParameteri",
    "glTexSubImage2D",
    "glUniform1f",
    "glUniform1i",
    "glUniform2f",
    "glUniform4f",
    "glUniformMatrix4fv",
    "glUseProgram",
    "glVertexAttribPointer",
    "glViewport",
]
