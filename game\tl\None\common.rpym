﻿
translate None strings:

    # renpy/common/00accessibility.rpy:28
    old "Self-voicing disabled."
    new "Self-voicing disabled."

    # renpy/common/00accessibility.rpy:29
    old "Clipboard voicing enabled. "
    new "Clipboard voicing enabled. "

    # renpy/common/00accessibility.rpy:30
    old "Self-voicing enabled. "
    new "Self-voicing enabled. "

    # renpy/common/00accessibility.rpy:32
    old "bar"
    new "bar"

    # renpy/common/00accessibility.rpy:33
    old "selected"
    new "selected"

    # renpy/common/00accessibility.rpy:34
    old "viewport"
    new "viewport"

    # renpy/common/00accessibility.rpy:35
    old "horizontal scroll"
    new "horizontal scroll"

    # renpy/common/00accessibility.rpy:36
    old "vertical scroll"
    new "vertical scroll"

    # renpy/common/00accessibility.rpy:37
    old "activate"
    new "activate"

    # renpy/common/00accessibility.rpy:38
    old "deactivate"
    new "deactivate"

    # renpy/common/00accessibility.rpy:39
    old "increase"
    new "increase"

    # renpy/common/00accessibility.rpy:40
    old "decrease"
    new "decrease"

    # renpy/common/00accessibility.rpy:128
    old "Font Override"
    new "Font Override"

    # renpy/common/00accessibility.rpy:132
    old "Default"
    new "Default"

    # renpy/common/00accessibility.rpy:136
    old "DejaVu Sans"
    new "DejaVu Sans"

    # renpy/common/00accessibility.rpy:140
    old "Opendyslexic"
    new "Opendyslexic"

    # renpy/common/00accessibility.rpy:146
    old "Text Size Scaling"
    new "Text Size Scaling"

    # renpy/common/00accessibility.rpy:152
    old "Reset"
    new "Reset"

    # renpy/common/00accessibility.rpy:157
    old "Line Spacing Scaling"
    new "Line Spacing Scaling"

    # renpy/common/00accessibility.rpy:169
    old "Self-Voicing"
    new "Self-Voicing"

    # renpy/common/00accessibility.rpy:173
    old "Off"
    new "Off"

    # renpy/common/00accessibility.rpy:177
    old "Text-to-speech"
    new "Text-to-speech"

    # renpy/common/00accessibility.rpy:181
    old "Clipboard"
    new "Clipboard"

    # renpy/common/00accessibility.rpy:185
    old "Debug"
    new "Debug"

    # renpy/common/00accessibility.rpy:191
    old "The options on this menu are intended to improve accessibility. They may not work with all games, and some combinations of options may render the game unplayable. This is not an issue with the game or engine. For the best results when changing fonts, try to keep the text size the same as it originally was."
    new "The options on this menu are intended to improve accessibility. They may not work with all games, and some combinations of options may render the game unplayable. This is not an issue with the game or engine. For the best results when changing fonts, try to keep the text size the same as it originally was."

    # renpy/common/00accessibility.rpy:196
    old "Return"
    new "Return"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Monday"
    new "{#weekday}Monday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Tuesday"
    new "{#weekday}Tuesday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Wednesday"
    new "{#weekday}Wednesday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Thursday"
    new "{#weekday}Thursday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Friday"
    new "{#weekday}Friday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Saturday"
    new "{#weekday}Saturday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Sunday"
    new "{#weekday}Sunday"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Mon"
    new "{#weekday_short}Mon"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Tue"
    new "{#weekday_short}Tue"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Wed"
    new "{#weekday_short}Wed"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Thu"
    new "{#weekday_short}Thu"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Fri"
    new "{#weekday_short}Fri"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sat"
    new "{#weekday_short}Sat"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sun"
    new "{#weekday_short}Sun"

    # renpy/common/00action_file.rpy:47
    old "{#month}January"
    new "{#month}January"

    # renpy/common/00action_file.rpy:47
    old "{#month}February"
    new "{#month}February"

    # renpy/common/00action_file.rpy:47
    old "{#month}March"
    new "{#month}March"

    # renpy/common/00action_file.rpy:47
    old "{#month}April"
    new "{#month}April"

    # renpy/common/00action_file.rpy:47
    old "{#month}May"
    new "{#month}May"

    # renpy/common/00action_file.rpy:47
    old "{#month}June"
    new "{#month}June"

    # renpy/common/00action_file.rpy:47
    old "{#month}July"
    new "{#month}July"

    # renpy/common/00action_file.rpy:47
    old "{#month}August"
    new "{#month}August"

    # renpy/common/00action_file.rpy:47
    old "{#month}September"
    new "{#month}September"

    # renpy/common/00action_file.rpy:47
    old "{#month}October"
    new "{#month}October"

    # renpy/common/00action_file.rpy:47
    old "{#month}November"
    new "{#month}November"

    # renpy/common/00action_file.rpy:47
    old "{#month}December"
    new "{#month}December"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jan"
    new "{#month_short}Jan"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Feb"
    new "{#month_short}Feb"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Mar"
    new "{#month_short}Mar"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Apr"
    new "{#month_short}Apr"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}May"
    new "{#month_short}May"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jun"
    new "{#month_short}Jun"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jul"
    new "{#month_short}Jul"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Aug"
    new "{#month_short}Aug"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Sep"
    new "{#month_short}Sep"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Oct"
    new "{#month_short}Oct"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Nov"
    new "{#month_short}Nov"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Dec"
    new "{#month_short}Dec"

    # renpy/common/00action_file.rpy:240
    old "%b %d, %H:%M"
    new "%b %d, %H:%M"

    # renpy/common/00action_file.rpy:353
    old "Save slot %s: [text]"
    new "Save slot %s: [text]"

    # renpy/common/00action_file.rpy:434
    old "Load slot %s: [text]"
    new "Load slot %s: [text]"

    # renpy/common/00action_file.rpy:487
    old "Delete slot [text]"
    new "Delete slot [text]"

    # renpy/common/00action_file.rpy:569
    old "File page auto"
    new "File page auto"

    # renpy/common/00action_file.rpy:571
    old "File page quick"
    new "File page quick"

    # renpy/common/00action_file.rpy:573
    old "File page [text]"
    new "File page [text]"

    # renpy/common/00action_file.rpy:631
    old "Page {}"
    new "Page {}"

    # renpy/common/00action_file.rpy:631
    old "Automatic saves"
    new "Automatic saves"

    # renpy/common/00action_file.rpy:631
    old "Quick saves"
    new "Quick saves"

    # renpy/common/00action_file.rpy:772
    old "Next file page."
    new "Next file page."

    # renpy/common/00action_file.rpy:845
    old "Previous file page."
    new "Previous file page."

    # renpy/common/00action_file.rpy:906
    old "Quick save complete."
    new "Quick save complete."

    # renpy/common/00action_file.rpy:924
    old "Quick save."
    new "Quick save."

    # renpy/common/00action_file.rpy:943
    old "Quick load."
    new "Quick load."

    # renpy/common/00action_other.rpy:375
    old "Language [text]"
    new "Language [text]"

    # renpy/common/00director.rpy:708
    old "The interactive director is not enabled here."
    new "The interactive director is not enabled here."

    # renpy/common/00director.rpy:1481
    old "⬆"
    new "⬆"

    # renpy/common/00director.rpy:1487
    old "⬇"
    new "⬇"

    # renpy/common/00director.rpy:1551
    old "Done"
    new "Done"

    # renpy/common/00director.rpy:1561
    old "(statement)"
    new "(statement)"

    # renpy/common/00director.rpy:1562
    old "(tag)"
    new "(tag)"

    # renpy/common/00director.rpy:1563
    old "(attributes)"
    new "(attributes)"

    # renpy/common/00director.rpy:1564
    old "(transform)"
    new "(transform)"

    # renpy/common/00director.rpy:1589
    old "(transition)"
    new "(transition)"

    # renpy/common/00director.rpy:1601
    old "(channel)"
    new "(channel)"

    # renpy/common/00director.rpy:1602
    old "(filename)"
    new "(filename)"

    # renpy/common/00director.rpy:1631
    old "Change"
    new "Change"

    # renpy/common/00director.rpy:1633
    old "Add"
    new "Add"

    # renpy/common/00director.rpy:1636
    old "Cancel"
    new "Cancel"

    # renpy/common/00director.rpy:1639
    old "Remove"
    new "Remove"

    # renpy/common/00director.rpy:1674
    old "Statement:"
    new "Statement:"

    # renpy/common/00director.rpy:1695
    old "Tag:"
    new "Tag:"

    # renpy/common/00director.rpy:1711
    old "Attributes:"
    new "Attributes:"

    # renpy/common/00director.rpy:1729
    old "Transforms:"
    new "Transforms:"

    # renpy/common/00director.rpy:1748
    old "Behind:"
    new "Behind:"

    # renpy/common/00director.rpy:1767
    old "Transition:"
    new "Transition:"

    # renpy/common/00director.rpy:1785
    old "Channel:"
    new "Channel:"

    # renpy/common/00director.rpy:1803
    old "Audio Filename:"
    new "Audio Filename:"

    # renpy/common/00gui.rpy:374
    old "Are you sure?"
    new "Are you sure?"

    # renpy/common/00gui.rpy:375
    old "Are you sure you want to delete this save?"
    new "Are you sure you want to delete this save?"

    # renpy/common/00gui.rpy:376
    old "Are you sure you want to overwrite your save?"
    new "Are you sure you want to overwrite your save?"

    # renpy/common/00gui.rpy:377
    old "Loading will lose unsaved progress.\nAre you sure you want to do this?"
    new "Loading will lose unsaved progress.\nAre you sure you want to do this?"

    # renpy/common/00gui.rpy:378
    old "Are you sure you want to quit?"
    new "Are you sure you want to quit?"

    # renpy/common/00gui.rpy:379
    old "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."
    new "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."

    # renpy/common/00gui.rpy:380
    old "Are you sure you want to end the replay?"
    new "Are you sure you want to end the replay?"

    # renpy/common/00gui.rpy:381
    old "Are you sure you want to begin skipping?"
    new "Are you sure you want to begin skipping?"

    # renpy/common/00gui.rpy:382
    old "Are you sure you want to skip to the next choice?"
    new "Are you sure you want to skip to the next choice?"

    # renpy/common/00gui.rpy:383
    old "Are you sure you want to skip unseen dialogue to the next choice?"
    new "Are you sure you want to skip unseen dialogue to the next choice?"

    # renpy/common/00keymap.rpy:267
    old "Failed to save screenshot as %s."
    new "Failed to save screenshot as %s."

    # renpy/common/00keymap.rpy:279
    old "Saved screenshot as %s."
    new "Saved screenshot as %s."

    # renpy/common/00library.rpy:195
    old "Skip Mode"
    new "Skip Mode"

    # renpy/common/00library.rpy:281
    old "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."
    new "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."

    # renpy/common/00preferences.rpy:233
    old "display"
    new "display"

    # renpy/common/00preferences.rpy:245
    old "transitions"
    new "transitions"

    # renpy/common/00preferences.rpy:254
    old "skip transitions"
    new "skip transitions"

    # renpy/common/00preferences.rpy:256
    old "video sprites"
    new "video sprites"

    # renpy/common/00preferences.rpy:265
    old "show empty window"
    new "show empty window"

    # renpy/common/00preferences.rpy:274
    old "text speed"
    new "text speed"

    # renpy/common/00preferences.rpy:282
    old "joystick"
    new "joystick"

    # renpy/common/00preferences.rpy:282
    old "joystick..."
    new "joystick..."

    # renpy/common/00preferences.rpy:289
    old "skip"
    new "skip"

    # renpy/common/00preferences.rpy:292
    old "skip unseen [text]"
    new "skip unseen [text]"

    # renpy/common/00preferences.rpy:297
    old "skip unseen text"
    new "skip unseen text"

    # renpy/common/00preferences.rpy:299
    old "begin skipping"
    new "begin skipping"

    # renpy/common/00preferences.rpy:303
    old "after choices"
    new "after choices"

    # renpy/common/00preferences.rpy:310
    old "skip after choices"
    new "skip after choices"

    # renpy/common/00preferences.rpy:312
    old "auto-forward time"
    new "auto-forward time"

    # renpy/common/00preferences.rpy:326
    old "auto-forward"
    new "auto-forward"

    # renpy/common/00preferences.rpy:333
    old "Auto forward"
    new "Auto forward"

    # renpy/common/00preferences.rpy:336
    old "auto-forward after click"
    new "auto-forward after click"

    # renpy/common/00preferences.rpy:345
    old "automatic move"
    new "automatic move"

    # renpy/common/00preferences.rpy:354
    old "wait for voice"
    new "wait for voice"

    # renpy/common/00preferences.rpy:363
    old "voice sustain"
    new "voice sustain"

    # renpy/common/00preferences.rpy:372
    old "self voicing"
    new "self voicing"

    # renpy/common/00preferences.rpy:381
    old "clipboard voicing"
    new "clipboard voicing"

    # renpy/common/00preferences.rpy:390
    old "debug voicing"
    new "debug voicing"

    # renpy/common/00preferences.rpy:399
    old "emphasize audio"
    new "emphasize audio"

    # renpy/common/00preferences.rpy:408
    old "rollback side"
    new "rollback side"

    # renpy/common/00preferences.rpy:418
    old "gl powersave"
    new "gl powersave"

    # renpy/common/00preferences.rpy:424
    old "gl framerate"
    new "gl framerate"

    # renpy/common/00preferences.rpy:427
    old "gl tearing"
    new "gl tearing"

    # renpy/common/00preferences.rpy:430
    old "font transform"
    new "font transform"

    # renpy/common/00preferences.rpy:433
    old "font size"
    new "font size"

    # renpy/common/00preferences.rpy:441
    old "font line spacing"
    new "font line spacing"

    # renpy/common/00preferences.rpy:460
    old "music volume"
    new "music volume"

    # renpy/common/00preferences.rpy:461
    old "sound volume"
    new "sound volume"

    # renpy/common/00preferences.rpy:462
    old "voice volume"
    new "voice volume"

    # renpy/common/00preferences.rpy:463
    old "mute music"
    new "mute music"

    # renpy/common/00preferences.rpy:464
    old "mute sound"
    new "mute sound"

    # renpy/common/00preferences.rpy:465
    old "mute voice"
    new "mute voice"

    # renpy/common/00preferences.rpy:466
    old "mute all"
    new "mute all"

    # renpy/common/00preferences.rpy:547
    old "Clipboard voicing enabled. Press 'shift+C' to disable."
    new "Clipboard voicing enabled. Press 'shift+C' to disable."

    # renpy/common/00preferences.rpy:549
    old "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."
    new "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."

    # renpy/common/00preferences.rpy:551
    old "Self-voicing enabled. Press 'v' to disable."
    new "Self-voicing enabled. Press 'v' to disable."

    # renpy/common/_compat/gamemenu.rpym:198
    old "Empty Slot."
    new "Empty Slot."

    # renpy/common/_compat/gamemenu.rpym:355
    old "Previous"
    new "Previous"

    # renpy/common/_compat/gamemenu.rpym:362
    old "Next"
    new "Next"

    # renpy/common/_compat/preferences.rpym:428
    old "Joystick Mapping"
    new "Joystick Mapping"

    # renpy/common/_developer/developer.rpym:38
    old "Developer Menu"
    new "Developer Menu"

    # renpy/common/_developer/developer.rpym:43
    old "Interactive Director (D)"
    new "Interactive Director (D)"

    # renpy/common/_developer/developer.rpym:45
    old "Reload Game (Shift+R)"
    new "Reload Game (Shift+R)"

    # renpy/common/_developer/developer.rpym:47
    old "Console (Shift+O)"
    new "Console (Shift+O)"

    # renpy/common/_developer/developer.rpym:49
    old "Variable Viewer"
    new "Variable Viewer"

    # renpy/common/_developer/developer.rpym:51
    old "Image Location Picker"
    new "Image Location Picker"

    # renpy/common/_developer/developer.rpym:53
    old "Filename List"
    new "Filename List"

    # renpy/common/_developer/developer.rpym:57
    old "Show Image Load Log (F4)"
    new "Show Image Load Log (F4)"

    # renpy/common/_developer/developer.rpym:60
    old "Hide Image Load Log (F4)"
    new "Hide Image Load Log (F4)"

    # renpy/common/_developer/developer.rpym:63
    old "Image Attributes"
    new "Image Attributes"

    # renpy/common/_developer/developer.rpym:90
    old "[name] [attributes] (hidden)"
    new "[name] [attributes] (hidden)"

    # renpy/common/_developer/developer.rpym:94
    old "[name] [attributes]"
    new "[name] [attributes]"

    # renpy/common/_developer/developer.rpym:143
    old "Nothing to inspect."
    new "Nothing to inspect."

    # renpy/common/_developer/developer.rpym:154
    old "Hide deleted"
    new "Hide deleted"

    # renpy/common/_developer/developer.rpym:154
    old "Show deleted"
    new "Show deleted"

    # renpy/common/_developer/developer.rpym:278
    old "Return to the developer menu"
    new "Return to the developer menu"

    # renpy/common/_developer/developer.rpym:443
    old "Rectangle: %r"
    new "Rectangle: %r"

    # renpy/common/_developer/developer.rpym:448
    old "Mouse position: %r"
    new "Mouse position: %r"

    # renpy/common/_developer/developer.rpym:453
    old "Right-click or escape to quit."
    new "Right-click or escape to quit."

    # renpy/common/_developer/developer.rpym:485
    old "Rectangle copied to clipboard."
    new "Rectangle copied to clipboard."

    # renpy/common/_developer/developer.rpym:488
    old "Position copied to clipboard."
    new "Position copied to clipboard."

    # renpy/common/_developer/developer.rpym:507
    old "Type to filter: "
    new "Type to filter: "

    # renpy/common/_developer/developer.rpym:635
    old "Textures: [tex_count] ([tex_size_mb:.1f] MB)"
    new "Textures: [tex_count] ([tex_size_mb:.1f] MB)"

    # renpy/common/_developer/developer.rpym:639
    old "Image cache: [cache_pct:.1f]% ([cache_size_mb:.1f] MB)"
    new "Image cache: [cache_pct:.1f]% ([cache_size_mb:.1f] MB)"

    # renpy/common/_developer/developer.rpym:649
    old "✔ "
    new "✔ "

    # renpy/common/_developer/developer.rpym:652
    old "✘ "
    new "✘ "

    # renpy/common/_developer/developer.rpym:657
    old "\n{color=#cfc}✔ predicted image (good){/color}\n{color=#fcc}✘ unpredicted image (bad){/color}\n{color=#fff}Drag to move.{/color}"
    new "\n{color=#cfc}✔ predicted image (good){/color}\n{color=#fcc}✘ unpredicted image (bad){/color}\n{color=#fff}Drag to move.{/color}"

    # renpy/common/_developer/inspector.rpym:38
    old "Displayable Inspector"
    new "Displayable Inspector"

    # renpy/common/_developer/inspector.rpym:61
    old "Size"
    new "Size"

    # renpy/common/_developer/inspector.rpym:65
    old "Style"
    new "Style"

    # renpy/common/_developer/inspector.rpym:71
    old "Location"
    new "Location"

    # renpy/common/_developer/inspector.rpym:122
    old "Inspecting Styles of [displayable_name!q]"
    new "Inspecting Styles of [displayable_name!q]"

    # renpy/common/_developer/inspector.rpym:139
    old "displayable:"
    new "displayable:"

    # renpy/common/_developer/inspector.rpym:145
    old "        (no properties affect the displayable)"
    new "        (no properties affect the displayable)"

    # renpy/common/_developer/inspector.rpym:147
    old "        (default properties omitted)"
    new "        (default properties omitted)"

    # renpy/common/_developer/inspector.rpym:185
    old "<repr() failed>"
    new "<repr() failed>"

    # renpy/common/_layout/classic_load_save.rpym:170
    old "a"
    new "a"

    # renpy/common/_layout/classic_load_save.rpym:179
    old "q"
    new "q"

    # renpy/common/00iap.rpy:217
    old "Contacting App Store\nPlease Wait..."
    new "Contacting App Store\nPlease Wait..."

    # renpy/common/00updater.rpy:375
    old "The Ren'Py Updater is not supported on mobile devices."
    new "The Ren'Py Updater is not supported on mobile devices."

    # renpy/common/00updater.rpy:494
    old "An error is being simulated."
    new "An error is being simulated."

    # renpy/common/00updater.rpy:678
    old "Either this project does not support updating, or the update status file was deleted."
    new "Either this project does not support updating, or the update status file was deleted."

    # renpy/common/00updater.rpy:692
    old "This account does not have permission to perform an update."
    new "This account does not have permission to perform an update."

    # renpy/common/00updater.rpy:695
    old "This account does not have permission to write the update log."
    new "This account does not have permission to write the update log."

    # renpy/common/00updater.rpy:722
    old "Could not verify update signature."
    new "Could not verify update signature."

    # renpy/common/00updater.rpy:997
    old "The update file was not downloaded."
    new "The update file was not downloaded."

    # renpy/common/00updater.rpy:1015
    old "The update file does not have the correct digest - it may have been corrupted."
    new "The update file does not have the correct digest - it may have been corrupted."

    # renpy/common/00updater.rpy:1071
    old "While unpacking {}, unknown type {}."
    new "While unpacking {}, unknown type {}."

    # renpy/common/00updater.rpy:1439
    old "Updater"
    new "Updater"

    # renpy/common/00updater.rpy:1446
    old "An error has occured:"
    new "An error has occured:"

    # renpy/common/00updater.rpy:1448
    old "Checking for updates."
    new "Checking for updates."

    # renpy/common/00updater.rpy:1450
    old "This program is up to date."
    new "This program is up to date."

    # renpy/common/00updater.rpy:1452
    old "[u.version] is available. Do you want to install it?"
    new "[u.version] is available. Do you want to install it?"

    # renpy/common/00updater.rpy:1454
    old "Preparing to download the updates."
    new "Preparing to download the updates."

    # renpy/common/00updater.rpy:1456
    old "Downloading the updates."
    new "Downloading the updates."

    # renpy/common/00updater.rpy:1458
    old "Unpacking the updates."
    new "Unpacking the updates."

    # renpy/common/00updater.rpy:1460
    old "Finishing up."
    new "Finishing up."

    # renpy/common/00updater.rpy:1462
    old "The updates have been installed. The program will restart."
    new "The updates have been installed. The program will restart."

    # renpy/common/00updater.rpy:1464
    old "The updates have been installed."
    new "The updates have been installed."

    # renpy/common/00updater.rpy:1466
    old "The updates were cancelled."
    new "The updates were cancelled."

    # renpy/common/00updater.rpy:1481
    old "Proceed"
    new "Proceed"

    # renpy/common/00compat.rpy:288
    old "Fullscreen"
    new "Fullscreen"

    # renpy/common/00gallery.rpy:592
    old "Image [index] of [count] locked."
    new "Image [index] of [count] locked."

    # renpy/common/00gallery.rpy:612
    old "prev"
    new "prev"

    # renpy/common/00gallery.rpy:613
    old "next"
    new "next"

    # renpy/common/00gallery.rpy:614
    old "slideshow"
    new "slideshow"

    # renpy/common/00gallery.rpy:615
    old "return"
    new "return"

    # renpy/common/00gltest.rpy:70
    old "Renderer"
    new "Renderer"

    # renpy/common/00gltest.rpy:74
    old "Automatically Choose"
    new "Automatically Choose"

    # renpy/common/00gltest.rpy:79
    old "Force Angle/DirectX Renderer"
    new "Force Angle/DirectX Renderer"

    # renpy/common/00gltest.rpy:83
    old "Force OpenGL Renderer"
    new "Force OpenGL Renderer"

    # renpy/common/00gltest.rpy:87
    old "Force Software Renderer"
    new "Force Software Renderer"

    # renpy/common/00gltest.rpy:93
    old "NPOT"
    new "NPOT"

    # renpy/common/00gltest.rpy:97
    old "Enable"
    new "Enable"

    # renpy/common/00gltest.rpy:101
    old "Disable"
    new "Disable"

    # renpy/common/00gltest.rpy:108
    old "Gamepad"
    new "Gamepad"

    # renpy/common/00gltest.rpy:122
    old "Calibrate"
    new "Calibrate"

    # renpy/common/00gltest.rpy:131
    old "Powersave"
    new "Powersave"

    # renpy/common/00gltest.rpy:145
    old "Framerate"
    new "Framerate"

    # renpy/common/00gltest.rpy:149
    old "Screen"
    new "Screen"

    # renpy/common/00gltest.rpy:153
    old "60"
    new "60"

    # renpy/common/00gltest.rpy:157
    old "30"
    new "30"

    # renpy/common/00gltest.rpy:163
    old "Tearing"
    new "Tearing"

    # renpy/common/00gltest.rpy:179
    old "Changes will take effect the next time this program is run."
    new "Changes will take effect the next time this program is run."

    # renpy/common/00gltest.rpy:186
    old "Quit"
    new "Quit"

    # renpy/common/00gltest.rpy:213
    old "Performance Warning"
    new "Performance Warning"

    # renpy/common/00gltest.rpy:218
    old "This computer is using software rendering."
    new "This computer is using software rendering."

    # renpy/common/00gltest.rpy:220
    old "This computer is not using shaders."
    new "This computer is not using shaders."

    # renpy/common/00gltest.rpy:222
    old "This computer is displaying graphics slowly."
    new "This computer is displaying graphics slowly."

    # renpy/common/00gltest.rpy:224
    old "This computer has a problem displaying graphics: [problem]."
    new "This computer has a problem displaying graphics: [problem]."

    # renpy/common/00gltest.rpy:229
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display. Updating DirectX could fix this problem."
    new "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display. Updating DirectX could fix this problem."

    # renpy/common/00gltest.rpy:231
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."
    new "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."

    # renpy/common/00gltest.rpy:236
    old "Update DirectX"
    new "Update DirectX"

    # renpy/common/00gltest.rpy:242
    old "Continue, Show this warning again"
    new "Continue, Show this warning again"

    # renpy/common/00gltest.rpy:246
    old "Continue, Don't show warning again"
    new "Continue, Don't show warning again"

    # renpy/common/00gltest.rpy:264
    old "Updating DirectX."
    new "Updating DirectX."

    # renpy/common/00gltest.rpy:268
    old "DirectX web setup has been started. It may start minimized in the taskbar. Please follow the prompts to install DirectX."
    new "DirectX web setup has been started. It may start minimized in the taskbar. Please follow the prompts to install DirectX."

    # renpy/common/00gltest.rpy:272
    old "{b}Note:{/b} Microsoft's DirectX web setup program will, by default, install the Bing toolbar. If you do not want this toolbar, uncheck the appropriate box."
    new "{b}Note:{/b} Microsoft's DirectX web setup program will, by default, install the Bing toolbar. If you do not want this toolbar, uncheck the appropriate box."

    # renpy/common/00gltest.rpy:276
    old "When setup finishes, please click below to restart this program."
    new "When setup finishes, please click below to restart this program."

    # renpy/common/00gltest.rpy:278
    old "Restart"
    new "Restart"

    # renpy/common/00gamepad.rpy:32
    old "Select Gamepad to Calibrate"
    new "Select Gamepad to Calibrate"

    # renpy/common/00gamepad.rpy:35
    old "No Gamepads Available"
    new "No Gamepads Available"

    # renpy/common/00gamepad.rpy:54
    old "Calibrating [name] ([i]/[total])"
    new "Calibrating [name] ([i]/[total])"

    # renpy/common/00gamepad.rpy:58
    old "Press or move the [control!r] [kind]."
    new "Press or move the [control!r] [kind]."

    # renpy/common/00gamepad.rpy:66
    old "Skip (A)"
    new "Skip (A)"

    # renpy/common/00gamepad.rpy:69
    old "Back (B)"
    new "Back (B)"

    # renpy/common/_errorhandling.rpym:538
    old "Open"
    new "Open"

    # renpy/common/_errorhandling.rpym:540
    old "Opens the traceback.txt file in a text editor."
    new "Opens the traceback.txt file in a text editor."

    # renpy/common/_errorhandling.rpym:542
    old "Copy BBCode"
    new "Copy BBCode"

    # renpy/common/_errorhandling.rpym:544
    old "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."
    new "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."

    # renpy/common/_errorhandling.rpym:546
    old "Copy Markdown"
    new "Copy Markdown"

    # renpy/common/_errorhandling.rpym:548
    old "Copies the traceback.txt file to the clipboard as Markdown for Discord."
    new "Copies the traceback.txt file to the clipboard as Markdown for Discord."

    # renpy/common/_errorhandling.rpym:577
    old "An exception has occurred."
    new "An exception has occurred."

    # renpy/common/_errorhandling.rpym:597
    old "Rollback"
    new "Rollback"

    # renpy/common/_errorhandling.rpym:599
    old "Attempts a roll back to a prior time, allowing you to save or choose a different choice."
    new "Attempts a roll back to a prior time, allowing you to save or choose a different choice."

    # renpy/common/_errorhandling.rpym:602
    old "Ignore"
    new "Ignore"

    # renpy/common/_errorhandling.rpym:606
    old "Ignores the exception, allowing you to continue."
    new "Ignores the exception, allowing you to continue."

    # renpy/common/_errorhandling.rpym:608
    old "Ignores the exception, allowing you to continue. This often leads to additional errors."
    new "Ignores the exception, allowing you to continue. This often leads to additional errors."

    # renpy/common/_errorhandling.rpym:612
    old "Reload"
    new "Reload"

    # renpy/common/_errorhandling.rpym:614
    old "Reloads the game from disk, saving and restoring game state if possible."
    new "Reloads the game from disk, saving and restoring game state if possible."

    # renpy/common/_errorhandling.rpym:617
    old "Console"
    new "Console"

    # renpy/common/_errorhandling.rpym:619
    old "Opens a console to allow debugging the problem."
    new "Opens a console to allow debugging the problem."

    # renpy/common/_errorhandling.rpym:629
    old "Quits the game."
    new "Quits the game."

    # renpy/common/_errorhandling.rpym:653
    old "Parsing the script failed."
    new "Parsing the script failed."

    # renpy/common/_errorhandling.rpym:679
    old "Opens the errors.txt file in a text editor."
    new "Opens the errors.txt file in a text editor."

    # renpy/common/_errorhandling.rpym:683
    old "Copies the errors.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."
    new "Copies the errors.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."

    # renpy/common/_errorhandling.rpym:687
    old "Copies the errors.txt file to the clipboard as Markdown for Discord."
    new "Copies the errors.txt file to the clipboard as Markdown for Discord."

    # renpy/common/00console.rpy:273
    old "Press <esc> to exit console. Type help for help.\n"
    new "Press <esc> to exit console. Type help for help.\n"

    # renpy/common/00console.rpy:277
    old "Ren'Py script enabled."
    new "Ren'Py script enabled."

    # renpy/common/00console.rpy:279
    old "Ren'Py script disabled."
    new "Ren'Py script disabled."

    # renpy/common/00console.rpy:526
    old "help: show this help"
    new "help: show this help"

    # renpy/common/00console.rpy:531
    old "commands:\n"
    new "commands:\n"

    # renpy/common/00console.rpy:541
    old " <renpy script statement>: run the statement\n"
    new " <renpy script statement>: run the statement\n"

    # renpy/common/00console.rpy:543
    old " <python expression or statement>: run the expression or statement"
    new " <python expression or statement>: run the expression or statement"

    # renpy/common/00console.rpy:551
    old "clear: clear the console history"
    new "clear: clear the console history"

    # renpy/common/00console.rpy:555
    old "exit: exit the console"
    new "exit: exit the console"

    # renpy/common/00console.rpy:563
    old "load <slot>: loads the game from slot"
    new "load <slot>: loads the game from slot"

    # renpy/common/00console.rpy:576
    old "save <slot>: saves the game in slot"
    new "save <slot>: saves the game in slot"

    # renpy/common/00console.rpy:587
    old "reload: reloads the game, refreshing the scripts"
    new "reload: reloads the game, refreshing the scripts"

    # renpy/common/00console.rpy:595
    old "watch <expression>: watch a python expression"
    new "watch <expression>: watch a python expression"

    # renpy/common/00console.rpy:621
    old "unwatch <expression>: stop watching an expression"
    new "unwatch <expression>: stop watching an expression"

    # renpy/common/00console.rpy:652
    old "unwatchall: stop watching all expressions"
    new "unwatchall: stop watching all expressions"

    # renpy/common/00console.rpy:669
    old "jump <label>: jumps to label"
    new "jump <label>: jumps to label"

    # renpy/common/00console.rpy:685
    old "short: Shorten the representation of objects on the console (default)."
    new "short: Shorten the representation of objects on the console (default)."

    # renpy/common/00console.rpy:690
    old "long: Print the full representation of objects on the console."
    new "long: Print the full representation of objects on the console."

