# Generated from 火车王

translate chinese strings:

    # renpy/common/00accessibility.rpy:28
    old "Self-voicing disabled."
    new "自发声禁用."

    # renpy/common/00accessibility.rpy:29
    old "Clipboard voicing enabled."
    new "剪贴板声音启用."

    # renpy/common/00accessibility.rpy:30
    old "Self-voicing enabled."
    new "启用自发声."

    # renpy/common/00accessibility.rpy:32
    old "bar"
    new "酒吧"

    # renpy/common/00accessibility.rpy:33
    old "selected"
    new "选定的"

    # renpy/common/00accessibility.rpy:34
    old "viewport"
    new "视口"

    # renpy/common/00accessibility.rpy:35
    old "horizontal scroll"
    new "水平卷轴"

    # renpy/common/00accessibility.rpy:36
    old "vertical scroll"
    new "垂直滚动"

    # renpy/common/00accessibility.rpy:37
    old "activate"
    new "激活"

    # renpy/common/00accessibility.rpy:38
    old "deactivate"
    new "停用"

    # renpy/common/00accessibility.rpy:39
    old "increase"
    new "增加"

    # renpy/common/00accessibility.rpy:40
    old "decrease"
    new "减少"

    # renpy/common/00accessibility.rpy:138
    old "Font Override"
    new "字体覆盖"

    # renpy/common/00accessibility.rpy:142
    old "Default"
    new "默认"

    # renpy/common/00accessibility.rpy:146
    old "DejaVu Sans"
    new "德贾武·桑斯"

    # renpy/common/00accessibility.rpy:150
    old "Opendyslexic"
    new "Opendyslexic"

    # renpy/common/00accessibility.rpy:156
    old "Text Size Scaling"
    new "文本大小缩放"

    # renpy/common/00accessibility.rpy:162
    old "Reset"
    new "复位"

    # renpy/common/00accessibility.rpy:168
    old "Line Spacing Scaling"
    new "行距缩放"

    # renpy/common/00accessibility.rpy:180
    old "High Contrast Text"
    new "高对比度文本"

    # renpy/common/00accessibility.rpy:182
    old "Enable"
    new "启用"

    # renpy/common/00accessibility.rpy:193
    old "Self-Voicing"
    new "自发声"

    # renpy/common/00accessibility.rpy:197
    old "Off"
    new "关闭"

    # renpy/common/00accessibility.rpy:201
    old "Text-to-speech"
    new "文语转换"

    # renpy/common/00accessibility.rpy:205
    old "Clipboard"
    new "剪贴板"

    # renpy/common/00accessibility.rpy:209
    old "Debug"
    new "调试"

    # renpy/common/00accessibility.rpy:215
    old "Self-Voicing Volume Drop"
    new "自浊音音量下降"

    # renpy/common/00accessibility.rpy:224
    old "The options on this menu are intended to improve accessibility. They may not work with all games, and some combinations of options may render the game unplayable. This is not an issue with the game or engine. For the best results when changing fonts, try to keep the text size the same as it originally was."
    new "此菜单上的选项旨在提高可访问性.它们可能不适用于所有游戏，某些选项的组合可能会使游戏不可玩.这不是游戏或引擎的问题.为了在更改字体时获得最佳效果，请尽量保持文本大小与原来的大小相同."

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Monday"
    new "{#weekday}星期一"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Tuesday"
    new "{#weekday}星期二"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Wednesday"
    new "{#weekday}星期三"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Thursday"
    new "{#weekday}星期四"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Friday"
    new "{#weekday}星期五"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Saturday"
    new "{#weekday}星期六"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Sunday"
    new "{#weekday}星期日"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Mon"
    new "{#weekday_short}Mon"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Tue"
    new "{#weekday_short}星期二"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Wed"
    new "{#weekday_short}结婚"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Thu"
    new "{#weekday_short}Thu"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Fri"
    new "{#weekday_short}星期五"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sat"
    new "{#weekday_short}Sat"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sun"
    new "{#weekday_short}太阳"

    # renpy/common/00action_file.rpy:47
    old "{#month}January"
    new "{#month}一月"

    # renpy/common/00action_file.rpy:47
    old "{#month}February"
    new "{#month}二月"

    # renpy/common/00action_file.rpy:47
    old "{#month}March"
    new "{#month}三月"

    # renpy/common/00action_file.rpy:47
    old "{#month}April"
    new "{#month}四月"

    # renpy/common/00action_file.rpy:47
    old "{#month}May"
    new "{#month}可能"

    # renpy/common/00action_file.rpy:47
    old "{#month}June"
    new "{#month}六月"

    # renpy/common/00action_file.rpy:47
    old "{#month}July"
    new "{#month}七月"

    # renpy/common/00action_file.rpy:47
    old "{#month}August"
    new "{#month}八月"

    # renpy/common/00action_file.rpy:47
    old "{#month}September"
    new "{#month}九月"

    # renpy/common/00action_file.rpy:47
    old "{#month}October"
    new "{#month}十月"

    # renpy/common/00action_file.rpy:47
    old "{#month}November"
    new "{#month}11月"

    # renpy/common/00action_file.rpy:47
    old "{#month}December"
    new "{#month}十二月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jan"
    new "{#month_short}一月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Feb"
    new "{#month_short}二月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Mar"
    new "{#month_short}Mar"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Apr"
    new "{#month_short}Apr"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}May"
    new "{#month_short}可以"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jun"
    new "{#month_short}Jun"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jul"
    new "{#month_short}Jul"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Aug"
    new "{#month_short}八月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Sep"
    new "{#month_short}Sep"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Oct"
    new "{#month_short}十月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Nov"
    new "{#month_short}11月份"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Dec"
    new "{#month_short}Dec"

    # renpy/common/00action_file.rpy:250
    old "%b %d, %H:%M"
    new "bd，h:m"

    # renpy/common/00action_file.rpy:363
    old "Save slot %s: [text]"
    new "保存插槽s:[text]"

    # renpy/common/00action_file.rpy:444
    old "Load slot %s: [text]"
    new "加载插槽s:[text]"

    # renpy/common/00action_file.rpy:497
    old "Delete slot [text]"
    new "删除插槽[text]"

    # renpy/common/00action_file.rpy:576
    old "File page auto"
    new "文件页自动"

    # renpy/common/00action_file.rpy:578
    old "File page quick"
    new "文件页快速"

    # renpy/common/00action_file.rpy:580
    old "File page [text]"
    new "文件页[text]"

    # renpy/common/00action_file.rpy:779
    old "Next file page."
    new "下一个文件页."

    # renpy/common/00action_file.rpy:851
    old "Previous file page."
    new "上一个文件页."

    # renpy/common/00action_file.rpy:912
    old "Quick save complete."
    new "快速保存完成."

    # renpy/common/00action_file.rpy:930
    old "Quick save."
    new "快救."

    # renpy/common/00action_file.rpy:949
    old "Quick load."
    new "快速装载."

    # renpy/common/00action_other.rpy:375
    old "Language [text]"
    new "语言[text]"

    # renpy/common/00director.rpy:708
    old "The interactive director is not enabled here."
    new "此处未启用交互式director."

    # renpy/common/00director.rpy:1481
    old "⬆"
    new "⬆"

    # renpy/common/00director.rpy:1487
    old "⬇"
    new "⬇"

    # renpy/common/00director.rpy:1551
    old "Done"
    new "完成"

    # renpy/common/00director.rpy:1561
    old "(statement)"
    new "（声明）"

    # renpy/common/00director.rpy:1562
    old "(tag)"
    new "（标签）"

    # renpy/common/00director.rpy:1563
    old "(attributes)"
    new "（属性）"

    # renpy/common/00director.rpy:1564
    old "(transform)"
    new "（变换）"

    # renpy/common/00director.rpy:1589
    old "(transition)"
    new "（过渡）"

    # renpy/common/00director.rpy:1601
    old "(channel)"
    new "（渠道）"

    # renpy/common/00director.rpy:1602
    old "(filename)"
    new "（文件名）"

    # renpy/common/00director.rpy:1631
    old "Change"
    new "改变"

    # renpy/common/00director.rpy:1633
    old "Add"
    new "添加"

    # renpy/common/00director.rpy:1636
    old "Cancel"
    new "取消"

    # renpy/common/00director.rpy:1639
    old "Remove"
    new "移除"

    # renpy/common/00director.rpy:1674
    old "Statement:"
    new "声明:"

    # renpy/common/00director.rpy:1695
    old "Tag:"
    new "标签:"

    # renpy/common/00director.rpy:1711
    old "Attributes:"
    new "属性:"

    # renpy/common/00director.rpy:1729
    old "Transforms:"
    new "转换:"

    # renpy/common/00director.rpy:1748
    old "Behind:"
    new "背后:"

    # renpy/common/00director.rpy:1767
    old "Transition:"
    new "过渡:"

    # renpy/common/00director.rpy:1785
    old "Channel:"
    new "频道:"

    # renpy/common/00director.rpy:1803
    old "Audio Filename:"
    new "音频文件名:"

    # renpy/common/00gui.rpy:423
    old "Are you sure?"
    new "你肯定吗？"

    # renpy/common/00gui.rpy:424
    old "Are you sure you want to delete this save?"
    new "您确定要删除此保存吗？"

    # renpy/common/00gui.rpy:425
    old "Are you sure you want to overwrite your save?"
    new "您确定要覆盖保存吗？"

    # renpy/common/00gui.rpy:426
    old "Loading will lose unsaved progress.\nAre you sure you want to do this?"
    new "加载将丢失未保存的进度.\n确实要这样做吗？"

    # renpy/common/00gui.rpy:427
    old "Are you sure you want to quit?"
    new "你确定要退出吗？"

    # renpy/common/00gui.rpy:428
    old "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."
    new "确实要返回主菜单吗？\n这将丢失未保存的进度."

    # renpy/common/00gui.rpy:429
    old "Are you sure you want to end the replay?"
    new "您确定要结束重播吗？"

    # renpy/common/00gui.rpy:430
    old "Are you sure you want to begin skipping?"
    new "您确定要开始跳过吗？"

    # renpy/common/00gui.rpy:431
    old "Are you sure you want to skip to the next choice?"
    new "您确定要跳到下一个选择吗？"

    # renpy/common/00gui.rpy:432
    old "Are you sure you want to skip unseen dialogue to the next choice?"
    new "您确定要跳过看不见的对话到下一个选项吗？"

    # renpy/common/00keymap.rpy:306
    old "Failed to save screenshot as %s."
    new "未能将截图保存为s."

    # renpy/common/00keymap.rpy:318
    old "Saved screenshot as %s."
    new "已将截图保存为s."

    # renpy/common/00library.rpy:195
    old "Skip Mode"
    new "跳过模式"

    # renpy/common/00library.rpy:281
    old "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."
    new "这个程序包含许多许可证下的自由软件，包括麻省理工学院许可证和GNU较小的通用公共许可证.可以在{a=https://www.renpy.org/l/license}这里{/a}找到完整的软件列表，包括完整源代码的链接."

    # renpy/common/00preferences.rpy:254
    old "display"
    new "显示"

    # renpy/common/00preferences.rpy:266
    old "transitions"
    new "过渡"

    # renpy/common/00preferences.rpy:275
    old "skip transitions"
    new "跳过转换"

    # renpy/common/00preferences.rpy:277
    old "video sprites"
    new "视频精灵"

    # renpy/common/00preferences.rpy:286
    old "show empty window"
    new "显示空窗口"

    # renpy/common/00preferences.rpy:295
    old "text speed"
    new "文本速度"

    # renpy/common/00preferences.rpy:303
    old "joystick"
    new "操纵杆"

    # renpy/common/00preferences.rpy:303
    old "joystick..."
    new "操纵杆..."

    # renpy/common/00preferences.rpy:310
    old "skip"
    new "跳过"

    # renpy/common/00preferences.rpy:313
    old "skip unseen [text]"
    new "跳过看不见[text]"

    # renpy/common/00preferences.rpy:318
    old "skip unseen text"
    new "跳过看不见的文本"

    # renpy/common/00preferences.rpy:320
    old "begin skipping"
    new "开始跳过"

    # renpy/common/00preferences.rpy:324
    old "after choices"
    new "选择之后"

    # renpy/common/00preferences.rpy:331
    old "skip after choices"
    new "在选择之后跳过"

    # renpy/common/00preferences.rpy:333
    old "auto-forward time"
    new "自动前进时间"

    # renpy/common/00preferences.rpy:347
    old "auto-forward"
    new "自动前进"

    # renpy/common/00preferences.rpy:354
    old "Auto forward"
    new "自动前进"

    # renpy/common/00preferences.rpy:357
    old "auto-forward after click"
    new "单击后自动转发"

    # renpy/common/00preferences.rpy:366
    old "automatic move"
    new "自动移动"

    # renpy/common/00preferences.rpy:375
    old "wait for voice"
    new "等待声音"

    # renpy/common/00preferences.rpy:384
    old "voice sustain"
    new "话音维持"

    # renpy/common/00preferences.rpy:393
    old "self voicing"
    new "自发声"

    # renpy/common/00preferences.rpy:402
    old "self voicing volume drop"
    new "自浊音音量下降"

    # renpy/common/00preferences.rpy:410
    old "clipboard voicing"
    new "剪贴板发声"

    # renpy/common/00preferences.rpy:419
    old "debug voicing"
    new "调试语音"

    # renpy/common/00preferences.rpy:428
    old "emphasize audio"
    new "强调音频"

    # renpy/common/00preferences.rpy:437
    old "rollback side"
    new "回滚侧"

    # renpy/common/00preferences.rpy:447
    old "gl powersave"
    new "gl powersave"

    # renpy/common/00preferences.rpy:453
    old "gl framerate"
    new "gl framerate"

    # renpy/common/00preferences.rpy:456
    old "gl tearing"
    new "gl撕裂"

    # renpy/common/00preferences.rpy:459
    old "font transform"
    new "字体变换"

    # renpy/common/00preferences.rpy:462
    old "font size"
    new "字体大小"

    # renpy/common/00preferences.rpy:470
    old "font line spacing"
    new "字体行距"

    # renpy/common/00preferences.rpy:478
    old "system cursor"
    new "系统光标"

    # renpy/common/00preferences.rpy:487
    old "renderer menu"
    new "渲染器菜单"

    # renpy/common/00preferences.rpy:490
    old "accessibility menu"
    new "辅助功能菜单"

    # renpy/common/00preferences.rpy:493
    old "high contrast text"
    new "高对比度文本"

    # renpy/common/00preferences.rpy:512
    old "music volume"
    new "音乐音量"

    # renpy/common/00preferences.rpy:513
    old "sound volume"
    new "音量"

    # renpy/common/00preferences.rpy:514
    old "voice volume"
    new "话音音量"

    # renpy/common/00preferences.rpy:515
    old "mute music"
    new "静音音乐"

    # renpy/common/00preferences.rpy:516
    old "mute sound"
    new "静音"

    # renpy/common/00preferences.rpy:517
    old "mute voice"
    new "无声的声音"

    # renpy/common/00preferences.rpy:518
    old "mute all"
    new "全部静音"

    # renpy/common/00preferences.rpy:599
    old "Clipboard voicing enabled. Press 'shift+C' to disable."
    new "剪贴板声音启用.按'Shift+C'禁用."

    # renpy/common/00preferences.rpy:601
    old "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."
    new "自我发声会说\“[renpy.display.tts.last]\”.按'Alt+Shift+V'禁用."

    # renpy/common/00preferences.rpy:603
    old "Self-voicing enabled. Press 'v' to disable."
    new "启用自发声.按'V'禁用."

    # renpy/common/_compat/gamemenu.rpym:198
    old "Empty Slot."
    new "空槽."

    # renpy/common/_compat/gamemenu.rpym:355
    old "Previous"
    new "上一次"

    # renpy/common/_compat/gamemenu.rpym:362
    old "Next"
    new "下一个"

    # renpy/common/_compat/preferences.rpym:428
    old "Joystick Mapping"
    new "操纵杆映射"

    # renpy/common/_developer/developer.rpym:38
    old "Developer Menu"
    new "开发人员菜单"

    # renpy/common/_developer/developer.rpym:43
    old "Interactive Director (D)"
    new "互动主任(D)"

    # renpy/common/_developer/developer.rpym:45
    old "Reload Game (Shift+R)"
    new "重新加载游戏(shift+r)"

    # renpy/common/_developer/developer.rpym:47
    old "Console (Shift+O)"
    new "控制台(shift+o)"

    # renpy/common/_developer/developer.rpym:49
    old "Variable Viewer"
    new "变量查看器"

    # renpy/common/_developer/developer.rpym:51
    old "Image Location Picker"
    new "图像位置选取器"

    # renpy/common/_developer/developer.rpym:53
    old "Filename List"
    new "文件名列表"

    # renpy/common/_developer/developer.rpym:57
    old "Show Image Load Log (F4)"
    new "显示图像加载日志(F4)"

    # renpy/common/_developer/developer.rpym:60
    old "Hide Image Load Log (F4)"
    new "隐藏图像加载日志(F4)"

    # renpy/common/_developer/developer.rpym:63
    old "Image Attributes"
    new "图像属性"

    # renpy/common/_developer/developer.rpym:90
    old "[name] [attributes] (hidden)"
    new "[name][attributes]（隐藏）"

    # renpy/common/_developer/developer.rpym:94
    old "[name] [attributes]"
    new "[name][attributes]"

    # renpy/common/_developer/developer.rpym:143
    old "Nothing to inspect."
    new "没什么可检查的."

    # renpy/common/_developer/developer.rpym:154
    old "Hide deleted"
    new "隐藏已删除"

    # renpy/common/_developer/developer.rpym:154
    old "Show deleted"
    new "显示已删除"

    # renpy/common/_developer/developer.rpym:278
    old "Return to the developer menu"
    new "返回到开发人员菜单"

    # renpy/common/_developer/developer.rpym:443
    old "Rectangle: %r"
    new "矩形:r"

    # renpy/common/_developer/developer.rpym:448
    old "Mouse position: %r"
    new "鼠标位置:r"

    # renpy/common/_developer/developer.rpym:453
    old "Right-click or escape to quit."
    new "右击或转义退出."

    # renpy/common/_developer/developer.rpym:485
    old "Rectangle copied to clipboard."
    new "矩形复制到剪贴板."

    # renpy/common/_developer/developer.rpym:488
    old "Position copied to clipboard."
    new "复制到剪贴板的位置."

    # renpy/common/_developer/developer.rpym:506
    old "Type to filter:"
    new "要筛选的类型:"

    # renpy/common/_developer/developer.rpym:631
    old "Textures: [tex_count] ([tex_size_mb:.1f] MB)"
    new "纹理:[tex_count]([tex_size_mb:.1f]MB)"

    # renpy/common/_developer/developer.rpym:635
    old "Image cache: [cache_pct:.1f]% ([cache_size_mb:.1f] MB)"
    new "映像缓存:[cache_pct:.1f]([cache_size_mb:.1f]MB)"

    # renpy/common/_developer/developer.rpym:645
    old "✔"
    new "✔"

    # renpy/common/_developer/developer.rpym:648
    old "✘"
    new "对此，作者提出了建议"

    # renpy/common/_developer/developer.rpym:653
    old "\n{color=#cfc}✔ predicted image (good){/color}\n{color=#fcc}✘ unpredicted image (bad){/color}\n{color=#fff}Drag to move.{/color}"
    new "\n{color=#cfc}✔预测图像（好）{/color}\n{color=#fcc}未预测图像（坏）{/color}\n{color=#fff}拖动以移动.{/color}"

    # renpy/common/_developer/inspector.rpym:38
    old "Displayable Inspector"
    new "可显示检查器"

    # renpy/common/_developer/inspector.rpym:61
    old "Size"
    new "尺寸"

    # renpy/common/_developer/inspector.rpym:65
    old "Style"
    new "风格"

    # renpy/common/_developer/inspector.rpym:71
    old "Location"
    new "地点"

    # renpy/common/_developer/inspector.rpym:122
    old "Inspecting Styles of [displayable_name!q]"
    new "检查[displayable_name!q]的样式"

    # renpy/common/_developer/inspector.rpym:139
    old "displayable:"
    new "可显示的:"

    # renpy/common/_developer/inspector.rpym:145
    old "(no properties affect the displayable)"
    new "（没有任何属性影响可显示的）"

    # renpy/common/_developer/inspector.rpym:147
    old "(default properties omitted)"
    new "（省略默认属性）"

    # renpy/common/_developer/inspector.rpym:185
    old "<repr() failed>"
    new "<repr()失败>"

    # renpy/common/_layout/classic_load_save.rpym:170
    old "a"
    new "a"

    # renpy/common/_layout/classic_load_save.rpym:179
    old "q"
    new "Q"

    # renpy/common/00iap.rpy:219
    old "Contacting App Store\nPlease Wait..."
    new "正在联系应用商店\n请稍候..."

    # renpy/common/00updater.rpy:391
    old "The Ren'Py Updater is not supported on mobile devices."
    new "移动设备不支持ren'py更新程序."

    # renpy/common/00updater.rpy:520
    old "An error is being simulated."
    new "正在模拟一个错误."

    # renpy/common/00updater.rpy:704
    old "Either this project does not support updating, or the update status file was deleted."
    new "该项目不支持更新，或者更新状态文件已删除."

    # renpy/common/00updater.rpy:718
    old "This account does not have permission to perform an update."
    new "此帐户没有执行更新的权限."

    # renpy/common/00updater.rpy:721
    old "This account does not have permission to write the update log."
    new "此帐户没有写入更新日志的权限."

    # renpy/common/00updater.rpy:746
    old "Could not verify update signature."
    new "无法验证更新签名."

    # renpy/common/00updater.rpy:1013
    old "The update file was not downloaded."
    new "更新文件未下载."

    # renpy/common/00updater.rpy:1031
    old "The update file does not have the correct digest - it may have been corrupted."
    new "更新文件没有正确的摘要-它可能已经损坏."

    # renpy/common/00updater.rpy:1181
    old "While unpacking {}, unknown type {}."
    new "解包{}, unknown type {}."

    # renpy/common/00updater.rpy:1553
    old "Updater"
    new "更新程序"

    # renpy/common/00updater.rpy:1560
    old "An error has occured:"
    new "发生错误:"

    # renpy/common/00updater.rpy:1562
    old "Checking for updates."
    new "正在检查更新."

    # renpy/common/00updater.rpy:1564
    old "This program is up to date."
    new "这个程序是最新的."

    # renpy/common/00updater.rpy:1566
    old "[u.version] is available. Do you want to install it?"
    new "[u.version]可用.你想安装它吗？"

    # renpy/common/00updater.rpy:1568
    old "Preparing to download the updates."
    new "正在准备下载更新."

    # renpy/common/00updater.rpy:1570
    old "Downloading the updates."
    new "正在下载更新."

    # renpy/common/00updater.rpy:1572
    old "Unpacking the updates."
    new "解包更新."

    # renpy/common/00updater.rpy:1574
    old "Finishing up."
    new "快结束了."

    # renpy/common/00updater.rpy:1576
    old "The updates have been installed. The program will restart."
    new "更新已经安装.程序将重新启动."

    # renpy/common/00updater.rpy:1578
    old "The updates have been installed."
    new "更新已经安装."

    # renpy/common/00updater.rpy:1580
    old "The updates were cancelled."
    new "更新被取消."

    # renpy/common/00updater.rpy:1595
    old "Proceed"
    new "继续"

    # renpy/common/00gallery.rpy:590
    old "Image [index] of [count] locked."
    new "已锁定[index]的图像[count]."

    # renpy/common/00gallery.rpy:610
    old "prev"
    new "前"

    # renpy/common/00gallery.rpy:611
    old "next"
    new "下一个"

    # renpy/common/00gallery.rpy:612
    old "slideshow"
    new "幻灯片放映"

    # renpy/common/00gallery.rpy:613
    old "return"
    new "返回"

    # renpy/common/00gltest.rpy:89
    old "Renderer"
    new "渲染器"

    # renpy/common/00gltest.rpy:93
    old "Automatically Choose"
    new "自动选择"

    # renpy/common/00gltest.rpy:100
    old "Force GL Renderer"
    new "强制总帐呈现器"

    # renpy/common/00gltest.rpy:105
    old "Force ANGLE Renderer"
    new "力角渲染器"

    # renpy/common/00gltest.rpy:110
    old "Force GLES Renderer"
    new "Force GLES渲染器"

    # renpy/common/00gltest.rpy:116
    old "Force GL2 Renderer"
    new "强制GL2渲染器"

    # renpy/common/00gltest.rpy:121
    old "Force ANGLE2 Renderer"
    new "强制ANGLE2渲染器"

    # renpy/common/00gltest.rpy:126
    old "Force GLES2 Renderer"
    new "强制GLES2渲染器"

    # renpy/common/00gltest.rpy:136
    old "Enable (No Blocklist)"
    new "启用（无阻止列表）"

    # renpy/common/00gltest.rpy:159
    old "Powersave"
    new "PowerSave"

    # renpy/common/00gltest.rpy:173
    old "Framerate"
    new "Framerate"

    # renpy/common/00gltest.rpy:177
    old "Screen"
    new "屏幕"

    # renpy/common/00gltest.rpy:181
    old "60"
    new "60"

    # renpy/common/00gltest.rpy:185
    old "30"
    new "30"

    # renpy/common/00gltest.rpy:191
    old "Tearing"
    new "撕裂"

    # renpy/common/00gltest.rpy:207
    old "Changes will take effect the next time this program is run."
    new "更改将在下次运行此程序时生效."

    # renpy/common/00gltest.rpy:242
    old "Performance Warning"
    new "性能警告"

    # renpy/common/00gltest.rpy:247
    old "This computer is using software rendering."
    new "这台计算机正在使用软件渲染."

    # renpy/common/00gltest.rpy:249
    old "This game requires use of GL2 that can't be initialised."
    new "这个游戏需要使用不能初始化的GL2."

    # renpy/common/00gltest.rpy:251
    old "This computer has a problem displaying graphics: [problem]."
    new "这台计算机显示图形时出现问题:[problem]."

    # renpy/common/00gltest.rpy:255
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."
    new "它的图形驱动程序可能已经过时或不能正确运行.这会导致图形显示缓慢或不正确."

    # renpy/common/00gltest.rpy:259
    old "The {a=edit:1:log.txt}log.txt{/a} file may contain information to help you determine what is wrong with your computer."
    new "{a=edit:1:log.txt}log.txt{/a}文件可能包含帮助您确定计算机故障的信息."

    # renpy/common/00gltest.rpy:264
    old "More details on how to fix this can be found in the {a=[url]}documentation{/a}."
    new "有关如何修复此问题的详细信息，请参阅{a=[url]}文档{/a}."

    # renpy/common/00gltest.rpy:269
    old "Continue, Show this warning again"
    new "继续，再次显示此警告"

    # renpy/common/00gltest.rpy:273
    old "Continue, Don't show warning again"
    new "继续，不要再显示警告"

    # renpy/common/00gltest.rpy:281
    old "Change render options"
    new "更改呈现选项"

    # renpy/common/00gamepad.rpy:32
    old "Select Gamepad to Calibrate"
    new "选择Gamepad进行校准"

    # renpy/common/00gamepad.rpy:35
    old "No Gamepads Available"
    new "没有可用的游戏手柄"

    # renpy/common/00gamepad.rpy:54
    old "Calibrating [name] ([i]/[total])"
    new "校准[name]([i]/[total])"

    # renpy/common/00gamepad.rpy:58
    old "Press or move the '[control!s]' [kind]."
    new "按或移动'[control!s][kind]."

    # renpy/common/00gamepad.rpy:68
    old "Skip (A)"
    new "跳过(A)"

    # renpy/common/00gamepad.rpy:71
    old "Back (B)"
    new "背(B)"

    # renpy/common/_errorhandling.rpym:553
    old "Open"
    new "开放"

    # renpy/common/_errorhandling.rpym:555
    old "Opens the traceback.txt file in a text editor."
    new "在文本编辑器中打开traceback.txt文件."

    # renpy/common/_errorhandling.rpym:557
    old "Copy BBCode"
    new "复制BBCode"

    # renpy/common/_errorhandling.rpym:559
    old "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."
    new "将traceback.txt文件作为论坛的BBcode复制到剪贴板中，如https://lemmasoft.renai.us/."

    # renpy/common/_errorhandling.rpym:561
    old "Copy Markdown"
    new "复制标记"

    # renpy/common/_errorhandling.rpym:563
    old "Copies the traceback.txt file to the clipboard as Markdown for Discord."
    new "将traceback.txt文件作为不一致的标记复制到剪贴板."

    # renpy/common/_errorhandling.rpym:592
    old "An exception has occurred."
    new "发生了异常."

    # renpy/common/_errorhandling.rpym:615
    old "Rollback"
    new "回滚"

    # renpy/common/_errorhandling.rpym:617
    old "Attempts a roll back to a prior time, allowing you to save or choose a different choice."
    new "尝试回滚到以前的时间，允许您保存或选择其他选项."

    # renpy/common/_errorhandling.rpym:620
    old "Ignore"
    new "忽略"

    # renpy/common/_errorhandling.rpym:624
    old "Ignores the exception, allowing you to continue."
    new "忽略异常，允许您继续."

    # renpy/common/_errorhandling.rpym:626
    old "Ignores the exception, allowing you to continue. This often leads to additional errors."
    new "忽略异常，允许您继续.这通常会导致额外的错误."

    # renpy/common/_errorhandling.rpym:630
    old "Reload"
    new "重新装填"

    # renpy/common/_errorhandling.rpym:632
    old "Reloads the game from disk, saving and restoring game state if possible."
    new "从磁盘重新加载游戏，如果可能的话保存和恢复游戏状态."

    # renpy/common/_errorhandling.rpym:635
    old "Console"
    new "控制台"

    # renpy/common/_errorhandling.rpym:637
    old "Opens a console to allow debugging the problem."
    new "打开控制台以允许调试问题."

    # renpy/common/_errorhandling.rpym:650
    old "Quits the game."
    new "退出游戏."

    # renpy/common/_errorhandling.rpym:671
    old "Parsing the script failed."
    new "分析脚本失败."
