label ck9:
    scene e01
    "Are you supporting the game?"
    menu:
        "Yes, I am supporting the game." if True:
            "Thank you, in my {a=https://www.patreon.com/posts/harem-of-v-0-5-6-56836929}Patreon posts{/a} you can find a code that will give you access to special scenes."


            $ code_input = renpy.input("Code?")
            if code_input == "Ilovetoeatpussy":
                $ code = True
                jump st
            elif True:
                "Wrong Code."
                menu:
                    "Try again" if True:
                        jump ck9
                    "Exit" if True:
                        jump st0
        "No, I am not supporting the game." if True:


            $ code = False
            jump st



label ff333:
    scene e1
    "Sorry, you can't use the code in the free version."
    jump ccn
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc
